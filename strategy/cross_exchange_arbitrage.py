import asyncio
from typing import Dict, Optional, Union
import ccxt.pro as ccxt
import logging
import json
import os
from ccxt.base.errors import DDoSProtection, RequestTimeout, NetworkError

class CrossExchangeArbitrage:
    """跨交易所合约价差对冲套利类（Bitget Maker + Binance Taker，百分比计算）"""

    def __init__(self, exchange1: ccxt.Exchange, exchange2: ccxt.Exchange, symbol='BTC/USDT:USDT',
                 margin=1000, leverage=10,margin_mode='isolated', spread_threshold_pct=0.1,spread_close_threshold_pct=0):
        self.ex1 = exchange1  # Bitget
        self.ex2 = exchange2  # Binance
        self.symbol = symbol
        self.margin = margin
        self.leverage = leverage
        self.margin_mode = margin_mode.lower()  # 转换为小写，规范化
        self.spread_threshold_pct = spread_threshold_pct
        self.spread_close_threshold_pct = spread_close_threshold_pct
        self.order_lock = asyncio.Lock()
        self.positions: Dict[str, Optional[Dict[str, Union[str, float]]]] = {
            self.ex1.id: None, self.ex2.id: None
        }
        self.fee_maker1 = None
        self.fee_taker1 = None
        self.fee_maker2 = None
        self.fee_taker2 = None
        self.max_timestamp_diff_ms = 200
        self.order_timeout_sec = 2
        self.taker_max_retries = 3
        self.hedge_mode = False # 强制为单向持仓
        self.is_running = False  # 运行状态标志
        self.set_fetch_order_params = {} # 用于 fetch_order 参数
        self.set_margin_mode_params = {} # 用于 set_margin_mode参数

        self.fees_config_path = "config/trading_fees_config.json" # 手续费配置文件

        # 获取交易所最小交易量
        self.min_amount_ex1 = max(self.ex1.markets[self.symbol].get('limits', {}).get('amount', {}).get('min', 0.0001),
                             0.0001)
        self.min_amount_ex2 = max(self.ex2.markets[self.symbol].get('limits', {}).get('amount', {}).get('min', 0.0001),
                             0.0001)
        self.min_amount_precision = max(self.min_amount_ex1, self.min_amount_ex2)

        # 获取并验证两个交易所的contractSize
        self.contract_size_ex1 = self.ex1.markets[self.symbol].get('contractSize', 1)
        self.contract_size_ex2 = self.ex2.markets[self.symbol].get('contractSize', 1)

        # 验证contractSize有效性
        if self.contract_size_ex1 <= 0 or self.contract_size_ex2 <= 0:
            raise ValueError(f"无效的contractSize: {self.ex1.id}={self.contract_size_ex1}, {self.ex2.id}={self.contract_size_ex2}")

        logging.info(f"[{self.symbol}] 初始化contractSize: {self.ex1.id}={self.contract_size_ex1}, {self.ex2.id}={self.contract_size_ex2}")

        # 验证 margin_mode 参数
        if self.margin_mode not in ['isolated', 'cross']:
            raise ValueError(f"无效的 margin_mode: {self.margin_mode}，必须为 'isolated' 或 'cross'")

        #交易所特定参数补充
        if self.ex1.id == 'bybit' or self.ex2.id == 'bybit':
            self.set_fetch_order_params['acknowledged']= True

        if self.ex1.id == 'okx' or self.ex2.id == 'okx':
            self.set_margin_mode_params['lever']= self.leverage
            self.set_margin_mode_params['marginMode']= self.margin_mode
            logging.info(f"为OKX交易所设置保证金模式参数: lever={self.leverage}")



    async def __fetch_fees(self):
        """查询手续费"""
        try:
            if not os.path.exists(self.fees_config_path):
                raise Exception(f"⚠️ 手续费配置文件不存在: {self.fees_config_path} ,检查文件路径")

            with open(self.fees_config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)

            fees_config = config.get('fees', {})

            if not fees_config:
                raise Exception(f"⚠️ 配置文件 {self.fees_config_path} 中没有找到手续费配置")

            self.fee_maker1 = fees_config.get(self.ex1.id, {}).get('maker')
            self.fee_taker1 = fees_config.get(self.ex1.id, {}).get('taker')
            self.fee_maker2 = fees_config.get(self.ex2.id, {}).get('maker')
            self.fee_taker2 = fees_config.get(self.ex2.id, {}).get('taker')
            logging.info(f"[{self.symbol}] {self.ex1.id}: Maker={self.fee_maker1 * 100:.4f}%, Taker={self.fee_taker1 * 100:.4f}%")
            logging.info(f"[{self.symbol}] {self.ex2.id}: Maker={self.fee_maker2 * 100:.4f}%, Taker={self.fee_taker2 * 100:.4f}%")

        except Exception as e:
            print(f"❌ 加载手续费配置失败: {type(e).__name__}: {str(e)}")
            raise

    def truncate_to_min_precision(self,exchange:ccxt.Exchange,amount: float) -> float:
        """将数值截断到最小交易量的精度，若小于最小精度则返回 0.0"""
        try:
            min_amount= float(exchange.amount_to_precision(self.symbol, amount))
            logging.info(f"[{self.symbol}] 截断: 输入={amount:.8e}, 输出={min_amount:.8f}")

            return min_amount
        except Exception as e:
            logging.error(f"[{self.symbol}] 截断失败: amount={amount:.8e}, 错误={type(e).__name__}: {str(e)}")
            return 0.0




    async def __place_maker_order_with_timeout(self, exchange, direction, price, amount, reduceOnly=False):
        """Bitget 下 Maker 限价单，带超时取消"""
        try:
            market = exchange.markets[self.symbol]
            price = exchange.price_to_precision(self.symbol, price)
            amount = float(exchange.amount_to_precision(self.symbol, amount))
            min_amount = max(market.get('limits', {}).get('amount', {}).get('min', 0.0001), 0.0001)

            if amount < min_amount:
                logging.error(
                    f"[{self.symbol}] [{exchange.id}] Maker 失败: 数量 {amount:.8f} 小于最小限制 {min_amount:.8f}"
                )
                return None

            params = {'postOnly': True,'marginMode': self.margin_mode,'reduceOnly': reduceOnly}
            order = await (exchange.create_limit_buy_order(self.symbol, amount, price, params)
                           if direction == 'buy'
                           else exchange.create_limit_sell_order(self.symbol, amount, price, params))
            if not order or 'id' not in order:
                logging.warning(f"[{self.symbol}] [{exchange.id}] Maker 下单失败: 订单无效")
                return None

            logging.info(
                f"[{self.symbol}] [{exchange.id}] Maker 下单: ID={order['id']}, 方向={direction}, 价格={float(price):.4f}, 数量={amount:.8f}"
            )

            try:
                order = await asyncio.wait_for(
                    self.__wait_order_filled(exchange, order['id']),
                    timeout=self.order_timeout_sec
                )
                if not order:
                    await exchange.cancel_order(order['id'], self.symbol)
                    logging.warning(f"[{self.symbol}] [{exchange.id}] 订单被拒绝，已取消: ID={order['id']}")
                    return None


                order = await exchange.fetch_order(order['id'], self.symbol, self.set_fetch_order_params)
                filled = float(order.get('filled', 0))
                remaining = float(order.get('remaining', amount))
                logging.info(
                    f"[{self.symbol}] [{exchange.id}] Maker 订单成交: ID={order['id']}, 成交={filled:.8f}, 剩余={remaining:.8f}"
                )
                return order

            except asyncio.TimeoutError:
                order = await exchange.fetch_order(order['id'], self.symbol, self.set_fetch_order_params)
                if not order:
                    logging.warning(f"[{self.symbol}] [{exchange.id}] 超时后订单查询失败: ID={order['id']}")
                    return None
                filled = float(order.get('filled', 0))
                remaining = float(order.get('remaining', amount))
                if filled > 0:
                    logging.info(
                        f"[{self.symbol}] [{exchange.id}] Maker 订单超时部分成交: ID={order['id']}, 成交={filled:.8f}, 剩余={remaining:.8f}"
                    )
                    if remaining > 0:
                        try :
                            await exchange.cancel_order(order['id'], self.symbol)
                            logging.info(f"[{self.symbol}] [{exchange.id}] 取消剩余未成交部分: ID={order['id']}")
                            # 再次查询状态，确认最终成交量
                            order = await exchange.fetch_order(order['id'], self.symbol, self.set_fetch_order_params)
                            filled = float(order.get('filled', filled))
                            logging.info(f"[{self.symbol}] [{exchange.id}] 最终成交结果: ID={order['id']}, 成交={filled:.8f}")

                        except Exception as e:
                            logging.error(f"[{self.symbol}] [{exchange.id}] 剩余未成交部分取消失败: {type(e).__name__}: {str(e)}")
                            # 再次查询状态，确认最终成交量
                            order = await exchange.fetch_order(order['id'], self.symbol, self.set_fetch_order_params)
                            filled = float(order.get('filled', filled))
                            logging.info(f"[{self.symbol}] [{exchange.id}] 剩余未成交部分已经成交: ID={order['id']}, 成交={filled:.8f}")
                            return order

                    return order
                else:
                    try:
                        await exchange.cancel_order(order['id'], self.symbol)
                        logging.warning(f"[{self.symbol}] [{exchange.id}] Maker 订单超时无成交，已取消: ID={order['id']}")
                        # 再次查询状态，确认最终成交量
                        order = await exchange.fetch_order(order['id'], self.symbol, self.set_fetch_order_params)
                        filled = float(order.get('filled', filled))
                        logging.info(f"[{self.symbol}] [{exchange.id}] 最终成交结果: ID={order['id']}, 成交={filled:.8f}")
                        if filled > 0:
                            return order
                        return None
                    except Exception as e:
                        logging.error(f"[{self.symbol}] [{exchange.id}]订单超时取消失败: {type(e).__name__}: {str(e)}")
                        # 再次查询状态，确认最终成交量
                        order = await exchange.fetch_order(order['id'], self.symbol, self.set_fetch_order_params)
                        filled = float(order.get('filled', filled))
                        logging.info(f"[{self.symbol}] [{exchange.id}] 订单已经成交: ID={order['id']}, 成交={filled:.8f}")
                        return order


        except Exception as e:
            logging.error(f"[{self.symbol}] [{exchange.id}] Maker 下单失败: {type(e).__name__}: {str(e)}")
            return None

    async def __place_market_order(self, exchange, direction, amount, reduceOnly=False):
        """下 Taker 市价单"""
        try:
            market = exchange.markets[self.symbol]
            amount = float(exchange.amount_to_precision(self.symbol, amount))
            min_amount = max(market.get('limits', {}).get('amount', {}).get('min', 0.0001), 0.0001)

            if amount < min_amount:
                logging.error(
                    f"[{self.symbol}] [{exchange.id}] Taker 失败，数量 {amount:.8f} 小于最小限制 {min_amount:.8f}"
                )
                return None

            params ={'marginMode': self.margin_mode,'reduceOnly': reduceOnly}
            order = await (exchange.create_market_buy_order(self.symbol, amount, params)
                           if direction == 'buy'
                           else exchange.create_market_sell_order(self.symbol, amount, params))
            order_details = await exchange.fetch_order(order['id'], self.symbol, self.set_fetch_order_params)
            avg_price = order_details.get('average', order_details.get('price', 0))
            logging.info(
                f"[{self.symbol}] [{exchange.id}] Taker 下单: ID={order['id']}, 方向={direction}, 数量={amount:.8f}, 成交价格={avg_price:.4f}"
            )
            return order
        except Exception as e:
            logging.error(f"[{self.symbol}] [{exchange.id}] Taker 下单失败: {type(e).__name__}: {str(e)}")
            return None

    async def __wait_order_filled(self, exchange, order_id):
        """等待订单成交，处理部分成交"""
        try:
            while True:
                order = await exchange.fetch_order(order_id, self.symbol, self.set_fetch_order_params)
                if not order:
                    logging.warning(f"[{self.symbol}] [{exchange.id}] 订单状态未知: ID={order_id}")
                    return None
                if order['status'] == 'closed':
                    logging.info(f"[{self.symbol}] [{exchange.id}] 订单完全成交: ID={order_id}")
                    return order
                elif order['status'] in ['canceled', 'rejected']:
                    logging.warning(f"[{self.symbol}] [{exchange.id}] 订单取消/拒绝: ID={order_id}")
                    return None
                filled = float(order.get('filled', 0))
                if filled > 0:
                    remaining = float(order.get('remaining', order.get('amount', 0)))
                    logging.info(
                        f"[{self.symbol}] [{exchange.id}] 订单部分成交: ID={order_id}, 成交={filled:.8f}, 剩余={remaining:.8f}"
                    )

        except asyncio.CancelledError:
            logging.info(f"[{self.symbol}] [{exchange.id}] 协程超时被取消，查询最后状态后重新抛出异常")
            try:
                # 查询最后的订单状态
                final_order = await exchange.fetch_order(order_id, self.symbol, self.set_fetch_order_params)
                logging.info(f"[{self.symbol}] [{exchange.id}] 取消时订单状态: ID={order_id}, 成交={final_order.get('filled', 0) if final_order else 0}")
            except Exception as e:
                logging.error(f"[{self.symbol}] [{exchange.id}] 取消时查询订单失败: {e}")
            # 重新抛出CancelledError，让上层的wait_for捕获并转换为TimeoutError
            raise
        except Exception as e:
            logging.error(f"[{self.symbol}] [{exchange.id}] 检查订单失败: {type(e).__name__}: {str(e)}")
            return None

    async def __close_position(self, exchange, side, amount):
        """平仓（Bitget Maker, Binance Taker）"""
        try:
            market = exchange.markets[self.symbol]
            amount = float(exchange.amount_to_precision(self.symbol, amount))
            min_amount = max(market.get('limits', {}).get('amount', {}).get('min', 0.0001), 0.0001)

            if amount < min_amount:
                logging.error(
                    f"[{self.symbol}] [{exchange.id}] 平仓失败，数量 {amount:.8f} 小于最小限制 {min_amount:.8f}"
                )
                return None

            if exchange == self.ex1:  # Bitget Maker
                order_book = await exchange.watch_order_book(self.symbol, limit=5)
                price = order_book['bids'][0][0] if side == 'buy' else order_book['asks'][0][0]
                order = await self.__place_maker_order_with_timeout(exchange, side, price, amount,reduceOnly=True)
                if not order:
                    logging.warning(f"[{self.symbol}] [{exchange.id}] Maker 平仓下单失败: 方向={side}, 数量={amount:.8f}")
                    return None
                logging.info(f"[{self.symbol}] [{exchange.id}] Maker 平仓成功: 方向={side}, 数量={float(order.get('filled', 0)):.8f}")
                return order
            else:  # Binance Taker
                for attempt in range(self.taker_max_retries):
                    order = await self.__place_market_order(exchange, side, amount, reduceOnly=True)
                    if order:
                        logging.info(f"[{self.symbol}] [{exchange.id}] Taker 平仓成功: 方向={side}, 数量={float(order.get('filled', 0)):.8f}")
                        return order
                    logging.warning(f"[{self.symbol}] [{exchange.id}] Taker 平仓失败，尝试重试 ({attempt + 1}/{self.taker_max_retries})")
                logging.error(f"[{self.symbol}] [{exchange.id}] Taker 平仓失败，已达最大重试次数: 方向={side}, 数量={amount:.8f}")
                return None
        except Exception as e:
            logging.error(f"[{self.symbol}] [{exchange.id}] 平仓失败: {type(e).__name__}: {str(e)}")
            return None

    async def __monitor_task(self):
        """监控持仓"""
        try:
            logging.info(f"[{self.symbol}] 启动 WebSocket 持仓监控")

            tasks = [
                self.__monitor_total_assets(),
                self.__monitor_pnl()
            ]

            await asyncio.gather(*tasks, return_exceptions=True)

        except asyncio.CancelledError:
            logging.info(f"[{self.symbol}] 所有监控任务已取消")
        except Exception as e:
            logging.error(f"[{self.symbol}] 监控错误: {type(e).__name__}: {str(e)}")
            await asyncio.sleep(5)
            await self.__monitor_task()


    # async def __watch_order(self, exchange, order_id):
    #     """通过 WebSocket 监控指定订单状态，超时回退到 fetch_order"""
    #
    #     try:
    #         orders = await asyncio.wait_for(exchange.watch_orders(self.symbol), timeout=0.1)
    #         for order in orders:
    #             if order['id'] == order_id:
    #                 return order
    #     except asyncio.TimeoutError:
    #         logging.warning(f"watch_orders 查询超时，改用fetch_order")
    #         order = await exchange.fetch_order(order_id, self.symbol)
    #         return order
    #
    #     except Exception as e:
    #         logging.error(f"[{exchange.id}] 监控订单失败: {type(e).__name__}: {str(e)}")



    async def __monitor_pnl(self):
        """监控持仓盈亏"""
        try:
            while self.is_running:
                try:
                    positions1 = await self.ex1.fetch_positions([self.symbol])
                    positions2 = await self.ex2.fetch_positions([self.symbol])

                    ex1_total_pnl = 0.0
                    for pos in positions1:
                        if pos.get('contracts', 0) > 0:
                            unrealized_pnl = pos.get('unrealizedPnl', 0)
                            realized_pnl = pos.get('realizedPnl', 0)
                            # 确保 unrealized_pnl 和 realized_pnl 是有效数字
                            if unrealized_pnl is None:
                                logging.warning(f"[{self.symbol}] {self.ex1.id} unrealizedPnl 缺失或为 None，使用 0")
                                unrealized_pnl = 0.0
                            if realized_pnl is None:
                                logging.warning(f"[{self.symbol}] {self.ex1.id} realizedPnl 缺失或为 None，使用 0")
                                realized_pnl = 0.0
                            ex1_total_pnl = float(unrealized_pnl) + float(realized_pnl)
                            logging.info(f"[{self.symbol}] {self.ex1.id} PNL: unrealized={unrealized_pnl:.4f}, realized={realized_pnl:.4f}, total={ex1_total_pnl:.4f}")
                            break

                    ex2_total_pnl = 0.0
                    for pos in positions2:
                        if pos.get('contracts', 0) > 0:
                            unrealized_pnl = pos.get('unrealizedPnl', 0)
                            realized_pnl = pos.get('realizedPnl', 0)
                            # 确保 unrealized_pnl 和 realized_pnl 是有效数字
                            if unrealized_pnl is None:
                                logging.warning(f"[{self.symbol}] {self.ex2.id} unrealizedPnl 缺失或为 None，使用 0")
                                unrealized_pnl = 0.0
                            if realized_pnl is None:
                                logging.warning(f"[{self.symbol}] {self.ex2.id} realizedPnl 缺失或为 None，使用 0")
                                realized_pnl = 0.0
                            ex2_total_pnl = float(unrealized_pnl) + float(realized_pnl)
                            logging.info(f"[{self.symbol}] {self.ex2.id} PNL: unrealized={unrealized_pnl:.4f}, realized={realized_pnl:.4f}, total={ex2_total_pnl:.4f}")
                            break

                    total_pnl = ex1_total_pnl + ex2_total_pnl
                    logging.info(f"[{self.symbol}] 总盈亏(未实现盈亏)={total_pnl:.4f} USDT")

                    await asyncio.sleep(1)  # 控制查询频率
                except Exception as e:
                    logging.error(f"[{self.symbol}] PNL 计算错误: {type(e).__name__}: {str(e)}")
                    await asyncio.sleep(5)  # 出错后等待更长时间
        except asyncio.CancelledError:
            logging.info(f"[{self.symbol}] 盈亏监控任务取消")

    async def __monitor_total_assets(self):
        """实时监控并打印两个交易所合约账户资产总和"""
        try:
            while self.is_running:
                try:
                    # 获取 Bitget 合约账户余额
                    balance1 = await self.ex1.fetch_balance(params={'type': 'swap'})
                    usdt1 = float(balance1['total'].get('USDT', 0))

                    # 获取 Binance 合约账户余额
                    balance2 = await self.ex2.fetch_balance(params={'type': 'swap'})
                    usdt2 = float(balance2['total'].get('USDT', 0))

                    # 计算总和
                    total_usdt = usdt1 + usdt2

                    # 打印日志
                    logging.info(
                        f"[{self.symbol}] 账户资产总和: {self.ex1.id}={usdt1:.4f} USDT, {self.ex2.id}={usdt2:.4f} USDT, 总计={total_usdt:.4f} USDT"
                    )

                    # 每秒更新一次
                    await asyncio.sleep(1)

                except Exception as e:
                    logging.error(f"[{self.symbol}] 资产监控错误: {type(e).__name__}: {str(e)}")
                    await asyncio.sleep(5)

        except asyncio.CancelledError:
            logging.info(f"[{self.symbol}] 资产监控任务取消")
        except Exception as e:
            logging.error(f"[{self.symbol}] 资产监控异常: {type(e).__name__}: {str(e)}")
            await asyncio.sleep(5)
            await self.__monitor_total_assets()

    async def __get_spread(self):
        """获取价差（百分比和金额）并打印，使用 watch_order_book，校验时间戳"""
        try:
            # 并发执行两个交易所的订单簿获取
            order_book1, order_book2 = await asyncio.gather(
                self.ex1.watch_order_book(self.symbol, limit=1),
                self.ex2.watch_order_book(self.symbol, limit=1)
            )

            ts1 = order_book1.get('timestamp')
            ts2 = order_book2.get('timestamp')

            if ts1 is None or ts2 is None:
                logging.warning(f"[{self.symbol}] 时间戳缺失")
                return None

            time_diff_ms = abs(ts1 - ts2)
            if time_diff_ms > self.max_timestamp_diff_ms:
                logging.warning(
                    f"[{self.symbol}] 时间差过大: {self.ex1.id} ts={ts1:.0f}, {self.ex2.id} ts={ts2:.0f}, 差值={time_diff_ms:.0f} ms"
                )
                return None

            if not order_book1.get('bids') or not order_book1.get('asks') or \
                    not order_book2.get('bids') or not order_book2.get('asks'):
                logging.warning(
                    f"[{self.symbol}] 订单簿数据缺失: {self.ex1.id} bids={len(order_book1.get('bids', [])):.0f}, "
                    f"asks={len(order_book1.get('asks', [])):.0f} | {self.ex2.id} bids={len(order_book2.get('bids', [])):.0f}, "
                    f"asks={len(order_book2.get('asks', [])):.0f}"
                )
                return None

            bid1, ask1 = order_book1['bids'][0][0], order_book1['asks'][0][0]
            bid2, ask2 = order_book2['bids'][0][0], order_book2['asks'][0][0]

            if not all(isinstance(x, (int, float)) for x in (bid1, ask1, bid2, ask2)):
                logging.warning(f"[{self.symbol}] 价格数据无效: bid1={bid1}, ask1={ask1}, bid2={bid2}, ask2={ask2}")
                return None

            # 正确的Maker-Taker策略价差计算
            # 方向1：Ex1 Maker卖出(ask1) + Ex2 Taker买入(ask2)
            spread1_usdt = ask1 - ask2
            # 方向2：Ex1 Maker买入(bid1) + Ex2 Taker卖出(bid2)
            spread2_usdt = bid2 - bid1

            # 选择价差更大的方向
            if spread1_usdt >= spread2_usdt:
                # 选择方向1：Ex1 Maker卖出，Ex2 Taker买入
                spread_usdt = spread1_usdt
                spread_pct = spread_usdt / ask2 * 100
                direction = 'ex1_sell_ex2_buy'
                ex1_price, ex2_price = ask1, ask2  # Ex1 Maker卖出价，Ex2 Taker买入价
                # 填limit=1注释掉下面
                # ex2_depth = order_book2['asks'][0][1] + order_book2['asks'][1][1]
                ex2_depth = order_book2['asks'][0][1]
            else:
                # 选择方向2：Ex1 Maker买入，Ex2 Taker卖出
                spread_usdt = spread2_usdt
                spread_pct = spread_usdt / bid1 * 100
                direction = 'ex1_buy_ex2_sell'
                ex1_price, ex2_price = bid1, bid2  # Ex1 Maker买入价，Ex2 Taker卖出价
                # 填limit=1注释掉下面
                # ex2_depth = order_book2['bids'][0][1] + order_book2['bids'][1][1]
                ex2_depth = order_book2['bids'][0][1]

            # 添加详细的Maker-Taker价差计算日志（调试用）
            # logging.debug(
            #     f"[{self.symbol}] Maker-Taker价差: {self.ex1.id} bid={bid1:.4f}, ask={ask1:.4f} | "
            #     f"{self.ex2.id} bid={bid2:.4f}, ask={ask2:.4f} | "
            #     f"方向1(ask1-ask2)={spread1_usdt:.4f}, 方向2(bid2-bid1)={spread2_usdt:.4f} | "
            #     f"选择={direction}, 价差={spread_usdt:.4f} USDT ({spread_pct:.4f}%) | 时间差={time_diff_ms:.0f} ms"
            # )

            return {
                'spread_pct': spread_pct,
                'spread_usdt': spread_usdt,
                'direction': direction,
                'ex1_price': ex1_price,
                'ex2_price': ex2_price,
                'ex2_depth': ex2_depth
            }
        except Exception as e:
            logging.error(f"[{self.symbol}] 错误: {type(e).__name__}: {str(e)}")
            return None

    async def __verify_positions(self, exchange, expected_pos: Optional[Dict[str, Union[str, float]]], context: str):
        """验证交易所的实际持仓是否与预期记录一致，如果不一致则进行市价平仓修正"""
        try:
            positions = await exchange.fetch_positions([self.symbol])
            actual_amount = 0.0
            actual_direction = None

            # 查找当前symbol的持仓
            for pos in positions:
                if pos.get('symbol') == self.symbol and pos.get('contracts', 0) > 0:
                    actual_amount = float(pos['contracts'])
                    actual_direction = 'buy' if pos['side'] == 'long' else 'sell'
                    break

            # 获取预期持仓
            expected_amount = expected_pos['amount'] if expected_pos else 0.0
            expected_direction = expected_pos['direction'] if expected_pos else None

            # 使用交易所的最小精度作为比较阈值
            min_amount = max(exchange.markets[self.symbol].get('limits', {}).get('amount', {}).get('min', 0.0001), 0.0001)
            
            # 验证持仓数量和方向
            amount_diff = abs(actual_amount - expected_amount)
            amount_match = amount_diff < min_amount
            direction_match = actual_direction == expected_direction

            if amount_match and direction_match:
                logging.info(
                    f"[{self.symbol}] [{exchange.id}] {context} 持仓验证通过: 实际={actual_amount:.8f} ({actual_direction or '无'}), "
                    f"预期={expected_amount:.8f} ({expected_direction or '无'})"
                )
                return True
            else:
                logging.warning(
                    f"[{self.symbol}] [{exchange.id}] {context} 持仓不一致: 实际={actual_amount:.8f} ({actual_direction or '无'}), "
                    f"预期={expected_amount:.8f} ({expected_direction or '无'}), 差异={amount_diff:.8f}"
                )

                # 严重错误：方向不一致且都不为None
                if not direction_match and actual_direction is not None and expected_direction is not None:
                    logging.error(f"[{self.symbol}] 严重错误!!! [{exchange.id}] {context} 持仓方向不一致: 预期={expected_direction}, 实际={actual_direction}")
                    
                    # 先平掉实际持仓
                    if actual_amount > 0:
                        close_direction = 'sell' if actual_direction == 'buy' else 'buy'
                        logging.info(f"[{self.symbol}] [{exchange.id}] 市价平仓错误方向持仓: 方向={close_direction}, 数量={actual_amount:.8f}")
                        
                        close_order = await self.__place_market_order(exchange, close_direction, actual_amount, reduceOnly=True)
                        if close_order:
                            logging.info(f"[{self.symbol}] [{exchange.id}] 错误方向持仓平仓成功")
                            # 清除持仓记录
                            self.positions[exchange.id] = None
                        else:
                            logging.error(f"[{self.symbol}] [{exchange.id}] 错误方向持仓平仓失败")
                            return False
                    return False
                
                # 数量不一致但方向一致，进行数量修正
                elif direction_match and not amount_match:
                    logging.warning(f"[{self.symbol}] [{exchange.id}] {context} 持仓数量不一致，需要修正")
                    
                    # 如果实际持仓多于预期持仓，平掉多余部分
                    if actual_amount > expected_amount:
                        excess_amount = actual_amount - expected_amount
                        close_direction = 'sell' if actual_direction == 'buy' else 'buy'
                        
                        # 截断到最小精度
                        excess_amount = self.truncate_to_min_precision(exchange, excess_amount)
                        
                        if excess_amount > 0:
                            logging.info(f"[{self.symbol}] [{exchange.id}] 市价平仓多余持仓: 方向={close_direction}, 数量={excess_amount:.8f}")
                            
                            close_order = await self.__place_market_order(exchange, close_direction, excess_amount, reduceOnly=True)
                            if close_order:
                                closed_amount = float(close_order.get('filled', 0))
                                logging.info(f"[{self.symbol}] [{exchange.id}] 多余持仓平仓成功: 成交={closed_amount:.8f}")
                                
                                # 更新持仓记录
                                if self.positions[exchange.id]:
                                    self.positions[exchange.id]["amount"] = max(0, self.positions[exchange.id]["amount"] - closed_amount)
                                    if self.positions[exchange.id]["amount"] <= min_amount:
                                        self.positions[exchange.id] = None
                                
                                # 重新验证
                                return await self.__verify_positions(exchange, expected_pos, f"{context}-retry")
                            else:
                                logging.error(f"[{self.symbol}] [{exchange.id}] 多余持仓平仓失败")
                                return False
                    
                    # 如果实际持仓少于预期持仓，只更新记录（不补仓，避免破坏套利对冲）
                    elif actual_amount < expected_amount:
                        logging.warning(f"[{self.symbol}] [{exchange.id}] 实际持仓少于预期，更新持仓记录")
                        if self.positions[exchange.id]:
                            self.positions[exchange.id]["amount"] = actual_amount
                            if actual_amount <= min_amount:
                                self.positions[exchange.id] = None
                        return True
                
                # 其他情况：一方为None的情况
                else:
                    if actual_direction is None and expected_direction is not None:
                        logging.error(f"[{self.symbol}] [{exchange.id}] {context} 预期有持仓但实际无持仓")
                        # 清除持仓记录
                        self.positions[exchange.id] = None
                        return False
                    elif actual_direction is not None and expected_direction is None:
                        logging.error(f"[{self.symbol}] [{exchange.id}] {context} 预期无持仓但实际有持仓，进行市价平仓")
                        
                        # 市价平掉意外持仓
                        close_direction = 'sell' if actual_direction == 'buy' else 'buy'
                        close_order = await self.__place_market_order(exchange, close_direction, actual_amount, reduceOnly=True)
                        if close_order:
                            logging.info(f"[{self.symbol}] [{exchange.id}] 意外持仓平仓成功")
                            self.positions[exchange.id] = None
                            return True
                        else:
                            logging.error(f"[{self.symbol}] [{exchange.id}] 意外持仓平仓失败")
                            return False
                    else:
                        return True

        except Exception as e:
            logging.error(f"[{self.symbol}] [{exchange.id}] {context} 持仓验证失败: {type(e).__name__}: {str(e)}")
            return False

    async def run(self):
        """运行套利策略（持续运行）"""
        monitor_task = None  # 初始化为 None
        try:
            self.is_running = True  # 设置运行状态
            logging.info(f"[{self.symbol}] 启动跨交易所套利: {self.symbol}")
            await self.ex1.load_markets()
            await self.ex2.load_markets()
            await self.__fetch_fees()
            # 创建并启动监控任务
            # monitor_task = asyncio.create_task(self.__monitor_task())

            logging.info(f"[{self.symbol}] {self.ex1.id} {self.symbol}最小交易量: {self.min_amount_ex1:.8f}")
            logging.info(f"[{self.symbol}] {self.ex2.id} {self.symbol}最小交易量: {self.min_amount_ex2:.8f}")
            logging.info(f"[{self.symbol}] 最小交易量: {self.min_amount_precision:.8f}")

            try:
                ex1_position_mode_response = await self.ex1.set_position_mode(self.hedge_mode, self.symbol)
                logging.info(f"[{self.symbol}] {self.ex1.id} 设置持仓模式响应: {ex1_position_mode_response}")
            except Exception as e:
                logging.error(f"[{self.symbol}] {self.ex1.id} 设置持仓模式失败: {type(e).__name__}: {str(e)}")

            try:
                ex2_position_mode_response = await self.ex2.set_position_mode(self.hedge_mode, self.symbol)
                logging.info(f"[{self.symbol}] {self.ex2.id} 设置持仓模式响应: {ex2_position_mode_response}")
            except Exception as e:
                logging.error(f"[{self.symbol}] {self.ex2.id} 设置持仓模式失败: {type(e).__name__}: {str(e)}")

            try:
                ex1_leverage_response=await self.ex1.set_leverage(self.leverage, self.symbol,self.set_margin_mode_params)
                logging.info(f"[{self.symbol}] {self.ex1.id} 杠杆设置响应: {ex1_leverage_response}")
            except Exception as e:
                logging.error(f"[{self.symbol}] {self.ex1.id} 设置杠杆失败: {type(e).__name__}: {str(e)}")

            try:
                ex2_leverage_response=await self.ex2.set_leverage(self.leverage, self.symbol,self.set_margin_mode_params)
                logging.info(f"[{self.symbol}] {self.ex2.id} 杠杆设置响应: {ex2_leverage_response}")
            except Exception as e:
                logging.error(f"[{self.symbol}] {self.ex2.id} 设置杠杆失败: {type(e).__name__}: {str(e)}")

            try:
                ex1_margin_mode_response = await self.ex1.set_margin_mode(self.margin_mode, self.symbol,self.set_margin_mode_params)  # bitget下单时候仍需设置marginMode,否则会默认为全仓模式
                logging.info(f"[{self.symbol}] {self.ex1.id} 设置保证金模式响应: {ex1_margin_mode_response}")
            except Exception as e:
                logging.error(f"[{self.symbol}] {self.ex1.id} 设置保证金模式失败: {type(e).__name__}: {str(e)}")

            try:
                ex2_margin_mode_response = await self.ex2.set_margin_mode(self.margin_mode, self.symbol,self.set_margin_mode_params)
                logging.info(f"[{self.symbol}] {self.ex2.id} 设置保证金模式响应: {ex2_margin_mode_response}")
            except Exception as e:
                logging.error(f"[{self.symbol}] {self.ex2.id}设置保证金模式失败: {type(e).__name__}: {str(e)}")

            while self.is_running:
                try:
                    spread_data = await self.__get_spread()
                    if not spread_data:
                        continue

                    spread_pct, spread_usdt = spread_data['spread_pct'], spread_data['spread_usdt']
                    direction = spread_data['direction']
                    ex1_price, ex2_price = spread_data['ex1_price'], spread_data['ex2_price']
                    ex2_depth = spread_data['ex2_depth']

                    balance1 = await self.ex1.fetch_balance()
                    balance2 = await self.ex2.fetch_balance()
                    usdt1 = balance1['total'].get('USDT', 0)
                    usdt2 = balance2['total'].get('USDT', 0)
                    max_usdt = min(usdt1, usdt2, self.margin)
                    if max_usdt <= 0:
                        logging.warning(
                            f"[{self.symbol}] 余额不足: {self.ex1.id} USDT={usdt1:.4f}, {self.ex2.id} USDT={usdt2:.4f}, 要求 {self.margin:.4f} USDT"
                        )
                        continue

                    # 计算币种数量（比如BTC数量），两边交易所下单相同的币种数量
                    coin_amount = float(max_usdt * self.leverage / ex1_price)

                    # 计算合约数量：币种数量 / contractSize = 合约数量
                    # 这样确保两边下单相同的币种数量
                    amount_ex1 = coin_amount / self.contract_size_ex1  # ex1需要的合约数量
                    amount_ex2 = coin_amount / self.contract_size_ex2  # ex2需要的合约数量

                    # 防止精度问题
                    amount_ex1 = self.truncate_to_min_precision(self.ex1, amount_ex1)
                    amount_ex2 = self.truncate_to_min_precision(self.ex2, amount_ex2)

                    logging.info(f"[{self.symbol}] 目标币种数量: {coin_amount:.8f}")
                    logging.info(f"[{self.symbol}] {self.ex1.id} contractSize={self.contract_size_ex1}, 合约数量={amount_ex1:.8f}")
                    logging.info(f"[{self.symbol}] {self.ex2.id} contractSize={self.contract_size_ex2}, 合约数量={amount_ex2:.8f}")

                    if not isinstance(ex2_depth, (int, float)) or not isinstance(coin_amount, (int, float)):
                        logging.warning(
                            f"[{self.symbol}] 深度或数量无效: ex2_depth={ex2_depth} ({type(ex2_depth)}), "
                            f"coin_amount={coin_amount} ({type(coin_amount)})"
                        )
                        continue

                    # total_fee_pct = 0  # debug
                    total_fee_pct=(self.fee_maker1 + self.fee_taker2)*2*100  # 百分比
                    if spread_pct <= total_fee_pct + self.spread_threshold_pct:
                        logging.info(
                            f"[{self.symbol}] 价差不足: {spread_pct:.4f}% < {total_fee_pct:.4f}% + {self.spread_threshold_pct:.4f}%"
                        )
                        continue

                    logging.info(
                        f"[{self.symbol}] 发现套利机会: 价差={spread_pct:.4f}% ({spread_usdt:.4f} USDT), 方向={direction}"
                    )

                    async with self.order_lock:
                        ex1_direction = 'sell' if direction == 'ex1_sell_ex2_buy' else 'buy'
                        ex2_direction = 'buy' if direction == 'ex1_sell_ex2_buy' else 'sell'
                        order1 = await self.__place_maker_order_with_timeout(self.ex1, ex1_direction, ex1_price, amount_ex1)
                        if not order1:
                            logging.warning(f"[{self.symbol}] {self.ex1.id} 下单失败，跳过")
                            continue

                        filled_amount_ex1 = float(order1.get('filled', 0))
                        if filled_amount_ex1 == 0:
                            logging.warning(f"[{self.symbol}] {self.ex1.id} 无成交，跳过")
                            continue

                        # 根据ex1的成交合约数量计算对应的币种数量，然后转换为ex2的合约数量
                        filled_coin_amount = filled_amount_ex1 * self.contract_size_ex1  # ex1成交的币种数量
                        filled_amount_ex2 = filled_coin_amount / self.contract_size_ex2  # ex2需要的合约数量
                        # 防止精度问题
                        filled_amount_ex2 = self.truncate_to_min_precision(self.ex2, filled_amount_ex2)

                        order2 = None
                        for attempt in range(self.taker_max_retries):
                            order2 = await self.__place_market_order(self.ex2, ex2_direction, filled_amount_ex2)
                            if order2:
                                break
                            logging.warning(f"[{self.symbol}] {self.ex2.id} 下单失败，尝试重试 ({attempt + 1}/{self.taker_max_retries})")

                        if not order2:
                            logging.warning(f"[{self.symbol}] {self.ex2.id} 下单失败，市价清除 {self.ex1.id} 仓位")
                            await self.__place_market_order(self.ex1, 'buy' if ex1_direction == 'sell' else 'sell',
                                                            filled_amount_ex1,reduceOnly=True)
                            continue

                        logging.info(f"[{self.symbol}] 套利下单成功: {self.ex1.id} {order1['id']}, {self.ex2.id} {order2['id']}")

                        self.positions[self.ex1.id] = {
                            'direction': ex1_direction,
                            'amount': filled_amount_ex1,
                            'price': ex1_price
                        }
                        self.positions[self.ex2.id] = {
                            'direction': ex2_direction,
                            'amount': filled_amount_ex2,
                            'price': order2.get('average', ex2_price)
                        }

                        # 验证套利下单后的持仓
                        ex1_open_verify_ok=await self.__verify_positions(self.ex1, self.positions[self.ex1.id], "open-position")
                        ex2_open_verify_ok=await self.__verify_positions(self.ex2, self.positions[self.ex2.id], "open-position")
                        if not ex1_open_verify_ok or not ex2_open_verify_ok:
                            logging.error(f"[{self.symbol}] open-position验证失败，取消所有订单,退出程序")
                            break


                    while True:
                        try:
                            new_spread_data = await self.__get_spread()
                            if not new_spread_data:
                                continue
                            if new_spread_data['spread_pct'] <= self.spread_close_threshold_pct:
                                logging.info(
                                    f"[{self.symbol}] 价差收敛，开始平仓: 当前价差={new_spread_data['spread_pct']:.4f}%"
                                )
                                ex1_side = 'buy' if ex1_direction == 'sell' else 'sell'
                                ex2_side = 'sell' if ex2_direction == 'buy' else 'buy'
                                async with self.order_lock:
                                    remaining_amount_ex1 = self.positions[self.ex1.id]['amount'] if self.positions[
                                        self.ex1.id] else 0
                                    remaining_amount_ex2 = self.positions[self.ex2.id]['amount'] if self.positions[
                                        self.ex2.id] else 0

                                    logging.info(f"[{self.symbol}] {self.ex1.id} 剩余待平仓数量: {remaining_amount_ex1}")
                                    logging.info(f"[{self.symbol}] {self.ex2.id} 剩余待平仓数量: {remaining_amount_ex2}")

                                    if remaining_amount_ex1 <= 0 and remaining_amount_ex2 <= 0:
                                        logging.info(f"[{self.symbol}] 无持仓，跳过平仓")
                                        self.positions[self.ex1.id] = None
                                        self.positions[self.ex2.id] = None
                                        break



                                    order1 = await self.__close_position(self.ex1, ex1_side, remaining_amount_ex1)
                                    if not order1:
                                        logging.warning(f"[{self.symbol}] {self.ex1.id} 平仓失败，继续平仓")
                                        continue
                                    ex1_filled = float(order1.get('filled', 0))
                                    if ex1_filled == 0:
                                        logging.warning(f"[{self.symbol}] {self.ex1.id} 平仓无成交，跳过")
                                        continue

                                    # 根据ex1的平仓合约数量计算对应的币种数量，然后转换为ex2的合约数量
                                    closed_coin_amount = ex1_filled * self.contract_size_ex1  # ex1平仓的币种数量
                                    ex2_close_amount = closed_coin_amount / self.contract_size_ex2  # ex2需要平仓的合约数量
                                    # 防止精度问题
                                    ex2_close_amount = self.truncate_to_min_precision(self.ex2, ex2_close_amount)

                                    order2 = await self.__close_position(self.ex2, ex2_side, ex2_close_amount)
                                    if not order2:
                                        logging.warning(f"[{self.symbol}] {self.ex2.id} 平仓失败，继续平仓")
                                        continue
                                    # 实际成交量
                                    ex2_filled = float(order2.get('filled', 0))

                                    logging.info(f"[{self.symbol}] 平仓完成，更新持仓")
                                    self.positions[self.ex1.id]['amount'] = self.truncate_to_min_precision(self.ex1, remaining_amount_ex1 - ex1_filled)
                                    self.positions[self.ex2.id]['amount'] = self.truncate_to_min_precision(self.ex2, remaining_amount_ex2 - ex2_filled)

                                    if self.positions[self.ex1.id]['amount'] <= 0:
                                        self.positions[self.ex1.id] = None

                                    if self.positions[self.ex2.id]['amount'] <= 0 :
                                        self.positions[self.ex2.id] = None

                                    # 验证平仓后的持仓
                                    ex1_close_verify_ok = await self.__verify_positions(self.ex1,self.positions[self.ex1.id],"close-position")
                                    ex2_close_verify_ok = await self.__verify_positions(self.ex2,self.positions[self.ex2.id],"close-position")


                                    if ex1_close_verify_ok and ex2_close_verify_ok:
                                        break
                                    else:
                                        logging.error(f"[{self.symbol}] close-position验证失败，取消所有订单,退出程序")
                                        raise Exception

                        except KeyboardInterrupt:
                            logging.info(f"[{self.symbol}] 接收到终止信号，关闭策略")
                            raise


                except KeyboardInterrupt:
                    logging.info(f"[{self.symbol}] 接收到终止信号，关闭策略")
                    break


                except DDoSProtection:
                    logging.error(f"[{self.symbol}] 请求过快，触发DDoS保护")
                    await asyncio.sleep(0.5)
                    continue
                except NetworkError:
                    logging.error(f"[{self.symbol}] 网络超时,继续运行")
                    continue

                except Exception as e:
                    logging.error(f"[{self.symbol}] 运行错误: {type(e).__name__}: {str(e)}")
                    logging.error(f"[{self.symbol}] 退出程序")
                    break


        finally:
            # 取消监控任务
            if monitor_task and not monitor_task.done():
                monitor_task.cancel()

            # 执行清仓操作
            await self.__emergency_close_all_positions()

            # 注意：不在这里关闭交易所连接，避免影响其他币种
            # 交易所连接的关闭应该由上层管理器统一处理
            logging.info(f"[{self.symbol}] 套利策略清理完成（保持交易所连接）")

    async def __emergency_close_all_positions(self):
        """紧急清空所有持仓"""
        logging.info(f"[{self.symbol}] 开始紧急清仓操作")

        try:
            # 首先取消所有未完成的委托订单
            logging.info(f"[{self.symbol}] 正在取消所有未完成的委托订单...")
            await self.__cancel_all_open_orders(self.ex1)
            await self.__cancel_all_open_orders(self.ex2)

            # 获取当前持仓，添加超时保护
            logging.info(f"[{self.symbol}] 正在获取持仓信息...")

            try:
                position_ex1 = await asyncio.wait_for(
                    self.ex1.fetch_positions([self.symbol]),
                    timeout=5.0
                )
                logging.info(f"[{self.symbol}] [{self.ex1.id}] 持仓信息获取成功")
            except asyncio.TimeoutError:
                logging.warning(f"[{self.symbol}] [{self.ex1.id}] 获取持仓信息超时，跳过清仓")
                position_ex1 = []
            except Exception as e:
                logging.error(f"[{self.symbol}] [{self.ex1.id}] 获取持仓信息失败: {type(e).__name__}: {str(e)}")
                position_ex1 = []

            try:
                position_ex2 = await asyncio.wait_for(
                    self.ex2.fetch_positions([self.symbol]),
                    timeout=5.0
                )
                logging.info(f"[{self.symbol}] [{self.ex2.id}] 持仓信息获取成功")
            except asyncio.TimeoutError:
                logging.warning(f"[{self.symbol}] [{self.ex2.id}] 获取持仓信息超时，跳过清仓")
                position_ex2 = []
            except Exception as e:
                logging.error(f"[{self.symbol}] [{self.ex2.id}] 获取持仓信息失败: {type(e).__name__}: {str(e)}")
                position_ex2 = []

            # 处理交易所1的持仓
            if position_ex1:
                await self.__emergency_close_exchange_position(self.ex1, position_ex1)
            else:
                logging.info(f"[{self.symbol}] [{self.ex1.id}] 无持仓信息，跳过清仓")

            # 处理交易所2的持仓
            if position_ex2:
                await self.__emergency_close_exchange_position(self.ex2, position_ex2)
            else:
                logging.info(f"[{self.symbol}] [{self.ex2.id}] 无持仓信息，跳过清仓")

            logging.info(f"[{self.symbol}] 紧急清仓操作完成")

        except KeyboardInterrupt:
            logging.warning(f"[{self.symbol}] 清仓过程中收到中断信号，强制退出")
        except Exception as e:
            logging.error(f"[{self.symbol}] 紧急清仓操作失败: {type(e).__name__}: {str(e)}")

    async def __cancel_all_open_orders(self, exchange):
        """取消指定交易所的所有未完成订单"""
        exchange_name = exchange.id
        try:
            logging.info(f"[{self.symbol}] [{exchange_name}] 正在获取未完成订单...")

            # 获取未完成订单，添加超时保护
            open_orders = await asyncio.wait_for(
                exchange.fetch_open_orders(self.symbol),
                timeout=5.0
            )

            if not open_orders:
                logging.info(f"[{self.symbol}] [{exchange_name}] 无未完成订单")
                return

            # 过滤出当前币种的订单，确保安全性
            target_orders = []
            for order in open_orders:
                order_symbol = order.get('symbol')
                if order_symbol == self.symbol:
                    target_orders.append(order)
                else:
                    logging.warning(f"[{self.symbol}] [{exchange_name}] 跳过非目标币种订单: ID={order.get('id', 'unknown')}, 币种={order_symbol}")

            if not target_orders:
                logging.info(f"[{self.symbol}] [{exchange_name}] 无当前币种({self.symbol})的未完成订单")
                return

            logging.info(f"[{self.symbol}] [{exchange_name}] 发现 {len(target_orders)} 个{self.symbol}未完成订单，开始取消...")

            # 取消当前币种的所有未完成订单
            cancelled_count = 0
            for order in target_orders:
                try:
                    order_id = order['id']
                    order_symbol = order.get('symbol', 'unknown')
                    order_side = order.get('side', 'unknown')
                    order_amount = order.get('amount', 0)

                    logging.info(f"[{self.symbol}] [{exchange_name}] 正在取消订单: ID={order_id}, 币种={order_symbol}, 方向={order_side}, 数量={order_amount}")

                    await asyncio.wait_for(
                        exchange.cancel_order(order_id, self.symbol),
                        timeout=5.0
                    )

                    cancelled_count += 1
                    logging.info(f"[{self.symbol}] [{exchange_name}] 订单取消成功: ID={order_id}")

                except asyncio.TimeoutError:
                    logging.warning(f"[{self.symbol}] [{exchange_name}] 取消订单超时: ID={order.get('id', 'unknown')}")
                except Exception as e:
                    logging.error(f"[{self.symbol}] [{exchange_name}] 取消订单失败: ID={order.get('id', 'unknown')}, 错误={type(e).__name__}: {str(e)}")

            logging.info(f"[{self.symbol}] [{exchange_name}] {self.symbol}订单取消完成: 成功取消 {cancelled_count}/{len(target_orders)} 个订单")

        except asyncio.TimeoutError:
            logging.warning(f"[{self.symbol}] [{exchange_name}] 获取未完成订单超时")
        except KeyboardInterrupt:
            logging.warning(f"[{self.symbol}] [{exchange_name}] 取消订单过程中收到中断信号")
        except Exception as e:
            logging.error(f"[{self.symbol}] [{exchange_name}] 取消订单操作失败: {type(e).__name__}: {str(e)}")

    async def __emergency_close_exchange_position(self, exchange, positions):
        """紧急清空单个交易所的持仓"""
        max_retries = 3
        exchange_name = exchange.id  # 自动获取交易所名称

        # 过滤出当前币种的持仓，确保安全性
        target_positions = []
        for pos in positions:
            pos_symbol = pos.get('symbol')
            if pos_symbol == self.symbol and pos.get('contracts', 0) > 0:
                target_positions.append(pos)
            elif pos_symbol != self.symbol and pos.get('contracts', 0) > 0:
                logging.warning(f"[{self.symbol}] [{exchange_name}] 跳过非目标币种持仓: 币种={pos_symbol}, 数量={pos.get('contracts', 0)}")

        if not target_positions:
            logging.info(f"[{self.symbol}] [{exchange_name}] 无当前币种({self.symbol})的持仓")
            return

        for pos in target_positions:
            actual_amount = float(pos['contracts'])
            actual_direction = 'buy' if pos['side'] == 'long' else 'sell'
            inverse_direction = 'sell' if actual_direction == 'buy' else 'buy'

            logging.info(f"[{self.symbol}] [{exchange_name}] 发现{self.symbol}持仓: 方向={actual_direction}, 数量={actual_amount:.8f}")

            # 重试清仓
            for attempt in range(max_retries):
                try:
                    # 截断到最小精度
                    close_amount = self.truncate_to_min_precision(exchange, actual_amount)
                    if close_amount <= 0:
                        logging.warning(f"[{self.symbol}] [{exchange_name}] 持仓数量太小，跳过清仓: {actual_amount:.8f}")
                        break

                    logging.info(f"[{self.symbol}] [{exchange_name}] 尝试清仓 (第{attempt + 1}次): 方向={inverse_direction}, 数量={close_amount:.8f}")

                    close_order = await asyncio.wait_for(
                        self.__place_market_order(exchange, inverse_direction, close_amount, reduceOnly=True),
                        timeout=10.0
                    )

                    if close_order:
                        # 安全获取成交数量，处理可能的 None 值
                        filled_amount = close_order.get('filled')
                        if filled_amount is None:
                            logging.warning(f"[{self.symbol}] [{exchange_name}] 订单返回的成交数量为 None，尝试重新查询订单状态")
                            try:
                                # 重新查询订单状态获取成交数量
                                order_details = await exchange.fetch_order(close_order['id'], self.symbol, self.set_fetch_order_params)
                                filled_amount = order_details.get('filled', 0)
                                if filled_amount is None:
                                    filled_amount = 0
                            except Exception as e:
                                logging.error(f"[{self.symbol}] [{exchange_name}] 查询订单状态失败: {type(e).__name__}: {str(e)}")
                                filled_amount = 0

                        filled_amount = float(filled_amount) if filled_amount is not None else 0.0
                        logging.info(f"[{self.symbol}] [{exchange_name}] 清仓成功: 成交数量={filled_amount:.8f}")

                        # 如果有成交，更新剩余持仓
                        if filled_amount > 0:
                            actual_amount -= filled_amount
                            if actual_amount <= self.truncate_to_min_precision(exchange, 0.0001):
                                logging.info(f"[{self.symbol}] [{exchange_name}] 持仓已完全清空")
                                break
                            else:
                                logging.info(f"[{self.symbol}] [{exchange_name}] 剩余持仓: {actual_amount:.8f}")
                        else:
                            logging.warning(f"[{self.symbol}] [{exchange_name}] 清仓订单无成交")
                    else:
                        logging.warning(f"[{self.symbol}] [{exchange_name}] 清仓失败 (第{attempt + 1}次)")

                except asyncio.TimeoutError:
                    logging.error(f"[{self.symbol}] [{exchange_name}] 清仓超时 (第{attempt + 1}次)")
                except KeyboardInterrupt:
                    logging.warning(f"[{self.symbol}] [{exchange_name}] 清仓过程中收到中断信号")
                    break

                # 如果不是最后一次尝试，等待后重试
                if attempt < max_retries - 1:
                    await asyncio.sleep(1)

            # 最终验证
            try:
                final_positions = await asyncio.wait_for(
                    exchange.fetch_positions([self.symbol]),
                    timeout=5.0
                )
                # 只检查当前币种的持仓
                current_symbol_position = None
                for final_pos in final_positions:
                    if final_pos.get('symbol') == self.symbol:
                        current_symbol_position = final_pos
                        break

                if current_symbol_position and current_symbol_position.get('contracts', 0) > 0:
                    remaining_amount = float(current_symbol_position['contracts'])
                    logging.warning(f" [{exchange_name}] {self.symbol}清仓后仍有剩余持仓: {remaining_amount:.8f}")
                else:
                    logging.info(f" [{exchange_name}] {self.symbol}持仓清空验证通过")

            except asyncio.TimeoutError:
                logging.warning(f"[{self.symbol}] [{exchange_name}] 清仓验证超时")
            except KeyboardInterrupt:
                logging.warning(f"[{self.symbol}] [{exchange_name}] 清仓验证过程中收到中断信号")
            except Exception as e:
                logging.error(f"[{self.symbol}] [{exchange_name}] 清仓验证失败: {type(e).__name__}: {str(e)}")

            break  # 只处理第一个有效持仓

    async def stop(self, close_connections=False):
        """
        停止套利策略

        Args:
            close_connections: 是否关闭交易所连接（默认False，避免影响其他币种）
        """
        logging.info(f"[{self.symbol}] 停止 {self.symbol} 套利策略...")
        self.is_running = False

        # 执行紧急清仓
        try:
            await self.__emergency_close_all_positions()
        except Exception as e:
            logging.error(f"[{self.symbol}] 停止时清仓失败: {str(e)}")

        # 只有在明确要求时才关闭交易所连接
        if close_connections:
            try:
                await self.ex1.close()
                await self.ex2.close()
                logging.info(f"[{self.symbol}] 交易所连接已关闭")
            except Exception as e:
                logging.error(f"[{self.symbol}] 关闭连接失败: {str(e)}")

        logging.info(f"[{self.symbol}] {self.symbol} 套利策略已停止")
